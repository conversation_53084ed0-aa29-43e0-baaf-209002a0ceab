package com.example.aimusicplayer.ui.widget

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.animation.ValueAnimator.AnimatorUpdateListener
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Point
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.RectF
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.util.Log
import android.view.View
import android.view.animation.AccelerateInterpolator
import android.view.animation.DecelerateInterpolator
import android.view.animation.LinearInterpolator
import androidx.core.content.res.ResourcesCompat
import com.example.aimusicplayer.R
import com.example.aimusicplayer.utils.ImageUtils

/**
 * 专辑封面视图
 * 实现黑胶唱片旋转动画效果
 */
class AlbumCoverView @JvmOverloads constructor(
    context: Context?,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 黑胶唱片背景
    private var discBitmap: Bitmap? = null
    private val discMatrix by lazy { Matrix() }
    private val discStartPoint by lazy { Point() } // 图片起始坐标
    private val discCenterPoint by lazy { Point() } // 旋转中心坐标
    private var discRotation = 0.0f

    // 唱针
    private var needleBitmap: Bitmap? = null
    private val needleMatrix by lazy { Matrix() }
    private val needleStartPoint by lazy { Point() }
    private val needleCenterPoint by lazy { Point() }
    private var needleRotation = NEEDLE_ROTATION_PLAY

    // 专辑封面
    private var coverBitmap: Bitmap? = null
    private val coverMatrix by lazy { Matrix() }
    private val coverStartPoint by lazy { Point() }
    private val coverCenterPoint by lazy { Point() }
    private var coverSize = 0

    // 绘制相关
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val circlePaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val coverRect = RectF()

    // 动画相关
    private val coverBorder: Drawable by lazy {
        ResourcesCompat.getDrawable(resources, R.drawable.bg_playing_cover_border, null)!!
    }

    private val rotationAnimator by lazy {
        ValueAnimator.ofFloat(0f, 360f).apply {
            duration = 20000
            repeatCount = ValueAnimator.INFINITE
            interpolator = LinearInterpolator()
            addUpdateListener(rotationUpdateListener)
            // 优化动画性能
            setFloatValues(0f, 360f)
        }
    }
    private val playAnimator by lazy {
        ValueAnimator.ofFloat(NEEDLE_ROTATION_PAUSE, NEEDLE_ROTATION_PLAY).apply {
            duration = 300
            addUpdateListener(animationUpdateListener)
        }
    }
    private val pauseAnimator by lazy {
        ValueAnimator.ofFloat(NEEDLE_ROTATION_PLAY, NEEDLE_ROTATION_PAUSE).apply {
            duration = 300
            addUpdateListener(animationUpdateListener)
        }
    }

    // 状态
    private var isPlaying = false
    private var lastRotation = 0f // 记录上次旋转角度，用于恢复旋转

    init {
        // 初始化画笔
        circlePaint.color = 0xFF111111.toInt() // 黑色
        circlePaint.style = Paint.Style.FILL

        // 设置混合模式，用于绘制圆形封面
        paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)

        // 初始化位图资源
        initBitmaps()
    }

    /**
     * 初始化位图资源
     * 安全地从资源创建Bitmap，支持Vector Drawable
     */
    private fun initBitmaps() {
        try {
            // 初始化黑胶唱片背景
            discBitmap = createBitmapFromResource(R.drawable.bg_playing_disc)

            // 初始化唱针
            needleBitmap = createBitmapFromResource(R.drawable.ic_playing_needle)

        } catch (e: Exception) {
            android.util.Log.e("AlbumCoverView", "初始化位图资源失败", e)
            // 创建默认位图以防止崩溃
            createDefaultBitmaps()
        }
    }

    /**
     * 从资源创建Bitmap，支持Vector Drawable
     */
    private fun createBitmapFromResource(resourceId: Int): Bitmap? {
        return try {
            val drawable = ResourcesCompat.getDrawable(resources, resourceId, null)
            drawable?.let { ImageUtils.drawableToBitmap(it) }
        } catch (e: Exception) {
            android.util.Log.e("AlbumCoverView", "从资源创建Bitmap失败: $resourceId", e)
            null
        }
    }

    /**
     * 创建默认位图以防止崩溃
     */
    private fun createDefaultBitmaps() {
        try {
            // 创建默认的黑胶唱片背景（简单的圆形）
            if (discBitmap == null) {
                discBitmap = createDefaultDiscBitmap()
            }

            // 创建默认的唱针（简单的矩形）
            if (needleBitmap == null) {
                needleBitmap = createDefaultNeedleBitmap()
            }
        } catch (e: Exception) {
            android.util.Log.e("AlbumCoverView", "创建默认位图失败", e)
        }
    }

    /**
     * 创建默认的黑胶唱片背景
     */
    private fun createDefaultDiscBitmap(): Bitmap {
        val size = 200
        val bitmap = Bitmap.createBitmap(size, size, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)

        // 绘制黑色圆形
        paint.color = 0xFF333333.toInt()
        canvas.drawCircle(size / 2f, size / 2f, size / 2f, paint)

        // 绘制中心小圆
        paint.color = 0xFF666666.toInt()
        canvas.drawCircle(size / 2f, size / 2f, size / 8f, paint)

        return bitmap
    }

    /**
     * 创建默认的唱针
     */
    private fun createDefaultNeedleBitmap(): Bitmap {
        val width = 20
        val height = 100
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)

        // 绘制灰色矩形作为唱针
        paint.color = 0xFF888888.toInt()
        canvas.drawRect(0f, 0f, width.toFloat(), height.toFloat(), paint)

        return bitmap
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        if (w > 0 && h > 0) {
            initSize()
        }
    }

    private fun initSize() {
        val unit = width.coerceAtMost(height) / 8

        // 安全地调整唱针大小
        needleBitmap?.let { needle ->
            needleBitmap = ImageUtils.resizeImage(needle, unit * 2, (unit * 3.33).toInt())
        }

        // 设置唱针位置（使用安全的默认值）
        val needleWidth = needleBitmap?.width ?: (unit * 2)
        val needleHeight = needleBitmap?.height ?: (unit * 3.33).toInt()

        needleStartPoint.x = (width / 2 - needleWidth / 5.5f).toInt()
        needleStartPoint.y = 0
        needleCenterPoint.x = width / 2
        needleCenterPoint.y = (needleWidth / 5.5f).toInt()

        // 安全地调整黑胶唱片大小
        discBitmap?.let { disc ->
            discBitmap = ImageUtils.resizeImage(disc, unit * 6, unit * 6)
        }

        // 设置黑胶唱片位置（使用安全的默认值）
        val discWidth = discBitmap?.width ?: (unit * 6)
        val discHeight = discBitmap?.height ?: (unit * 6)

        val discOffsetY = (needleHeight / 1.5).toInt()
        discStartPoint.x = (width - discWidth) / 2
        discStartPoint.y = discOffsetY
        discCenterPoint.x = width / 2
        discCenterPoint.y = discHeight / 2 + discOffsetY

        // 设置封面大小和位置
        coverSize = unit * 4
        coverStartPoint.x = (width - coverSize) / 2
        coverStartPoint.y = discOffsetY + (discHeight - coverSize) / 2
        coverCenterPoint.x = discCenterPoint.x
        coverCenterPoint.y = discCenterPoint.y
    }

    override fun onDraw(canvas: Canvas) {
        try {
            // 检查画布有效性
            if (width <= 0 || height <= 0) {
                return
            }

            // 保存画布状态，但避免不必要的saveLayer调用以提高性能
            val saveCount = canvas.save()

            // 1. 绘制黑胶唱片
            discBitmap?.let { disc ->
                if (!disc.isRecycled) {
                    try {
                        discMatrix.reset()
                        discMatrix.setRotate(
                            discRotation,
                            discCenterPoint.x.toFloat(),
                            discCenterPoint.y.toFloat()
                        )
                        discMatrix.preTranslate(discStartPoint.x.toFloat(), discStartPoint.y.toFloat())
                        canvas.drawBitmap(disc, discMatrix, null)
                    } catch (e: Exception) {
                        Log.e("AlbumCoverView", "绘制黑胶唱片失败", e)
                    }
                }
            }

            // 2. 绘制圆形封面
            val cover = coverBitmap
            if (cover != null && !cover.isRecycled) {
                try {
                    // 先绘制一个圆形
                    canvas.drawCircle(
                        coverCenterPoint.x.toFloat(),
                        coverCenterPoint.y.toFloat(),
                        coverSize / 2f,
                        circlePaint
                    )

                    // 使用SRC_IN模式绘制封面，这样封面就会被裁剪成圆形
                    coverMatrix.reset()
                    coverMatrix.setRotate(
                        discRotation, // 封面跟随唱片旋转
                        coverCenterPoint.x.toFloat(),
                        coverCenterPoint.y.toFloat()
                    )
                    coverMatrix.preTranslate(coverStartPoint.x.toFloat(), coverStartPoint.y.toFloat())
                    coverMatrix.preScale(
                        coverSize.toFloat() / cover.width,
                        coverSize.toFloat() / cover.height
                    )

                    // 使用混合模式绘制封面
                    canvas.drawBitmap(cover, coverMatrix, paint)
                } catch (e: Exception) {
                    Log.e("AlbumCoverView", "绘制专辑封面失败", e)
                }
            }

            // 3. 绘制唱针（可选，如果不需要唱针可以注释掉）
            needleBitmap?.let { needle ->
                if (!needle.isRecycled) {
                    try {
                        needleMatrix.reset()
                        needleMatrix.setRotate(
                            needleRotation,
                            needleCenterPoint.x.toFloat(),
                            needleCenterPoint.y.toFloat()
                        )
                        needleMatrix.preTranslate(needleStartPoint.x.toFloat(), needleStartPoint.y.toFloat())
                        canvas.drawBitmap(needle, needleMatrix, null)
                    } catch (e: Exception) {
                        Log.e("AlbumCoverView", "绘制唱针失败", e)
                    }
                }
            }

            // 恢复画布状态
            canvas.restoreToCount(saveCount)
        } catch (e: Exception) {
            Log.e("AlbumCoverView", "onDraw执行失败", e)
        }
    }

    fun initNeedle(isPlaying: Boolean) {
        needleRotation = if (isPlaying) NEEDLE_ROTATION_PLAY else NEEDLE_ROTATION_PAUSE
        invalidate()
    }

    /**
     * 设置专辑封面
     * @param bitmap 封面图片
     */
    fun setCoverBitmap(bitmap: Bitmap) {
        coverBitmap = bitmap
        invalidate()
    }

    /**
     * 开始播放动画
     */
    fun start() {
        if (isPlaying) {
            return
        }
        isPlaying = true
        rotationAnimator.start()
        playAnimator.start()
    }

    /**
     * 暂停播放动画
     */
    fun pause() {
        if (!isPlaying) {
            return
        }
        isPlaying = false
        rotationAnimator.pause()
        pauseAnimator.start()
    }

    /**
     * 重置动画状态
     */
    fun reset() {
        isPlaying = false
        discRotation = 0.0f
        rotationAnimator.cancel()
        invalidate()
    }

    /**
     * 唱片旋转动画更新监听器
     * 优化性能，减少不必要的重绘
     */
    private val rotationUpdateListener = AnimatorUpdateListener { animation ->
        try {
            val newRotation = animation.animatedValue as Float
            if (Math.abs(newRotation - discRotation) > 1f) { // 只有旋转角度变化超过1度才重绘
                discRotation = newRotation
                invalidate()
            } else {
                discRotation = newRotation
            }
        } catch (e: Exception) {
            Log.e("AlbumCoverView", "旋转动画更新失败", e)
        }
    }

    private val animationUpdateListener = AnimatorUpdateListener { animation ->
        try {
            val newRotation = animation.animatedValue as Float
            if (Math.abs(newRotation - needleRotation) > 0.5f) { // 唱针动画更精细
                needleRotation = newRotation
                invalidate()
            } else {
                needleRotation = newRotation
            }
        } catch (e: Exception) {
            Log.e("AlbumCoverView", "唱针动画更新失败", e)
        }
    }

    /**
     * 设置硬件加速和性能优化
     * 提高动画流畅度，解决OpenGL渲染问题
     */
    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        try {
            // 启用硬件加速，但添加错误处理
            setLayerType(LAYER_TYPE_HARDWARE, null)

            // 设置绘制缓存以提高性能
            isDrawingCacheEnabled = false // 禁用过时的绘制缓存

            // 优化视图属性
            setWillNotDraw(false) // 确保onDraw会被调用

            Log.d("AlbumCoverView", "硬件加速已启用")
        } catch (e: Exception) {
            Log.e("AlbumCoverView", "启用硬件加速失败，回退到软件渲染", e)
            // 如果硬件加速失败，回退到软件渲染
            setLayerType(LAYER_TYPE_SOFTWARE, null)
        }
    }

    /**
     * 清理资源，防止内存泄漏
     */
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        try {
            // 停止所有动画
            rotationAnimator.cancel()
            pauseAnimator.cancel()

            // 清理位图资源
            discBitmap?.let {
                if (!it.isRecycled) {
                    // 不要手动回收，让GC处理
                    Log.d("AlbumCoverView", "位图资源已标记清理")
                }
            }

            Log.d("AlbumCoverView", "视图资源已清理")
        } catch (e: Exception) {
            Log.e("AlbumCoverView", "清理资源时发生错误", e)
        }
    }



    companion object {
        private const val NEEDLE_ROTATION_PLAY = 0.0f
        private const val NEEDLE_ROTATION_PAUSE = -25.0f

        private const val COVER_BORDER_WIDTH = 6
    }
}
