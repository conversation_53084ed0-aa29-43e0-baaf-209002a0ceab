<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 百度语音SDK所需权限 -->
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS"/>

    <!-- Android 13 (API 33) 及以上版本需要区分的存储权限 -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />

    <!-- Android 13 新增的精细存储权限 -->
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />

    <!-- 应用所需的其他权限 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO"/>
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

    <!-- 通知权限 (Android 13+) -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!-- 前台服务权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <!-- 震动权限 -->
    <uses-permission android:name="android.permission.VIBRATE" />

    <!-- 唤醒锁权限 (ExoPlayer播放时保持设备唤醒) -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <application
        android:name=".MusicApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="轻聆"
        android:supportsRtl="true"
        android:theme="@style/Theme.AIMusicPlayer"
        android:requestLegacyExternalStorage="true"
        android:usesCleartextTraffic="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:hardwareAccelerated="true"
        android:largeHeap="true"
        tools:targetApi="33">

        <!-- 百度语音SDK必要的meta-data配置 -->
        <meta-data
            android:name="com.baidu.speech.APP_ID"
            android:value="118558442" />
        <meta-data
            android:name="com.baidu.speech.API_KEY"
            android:value="l07tTLiM8XdSVcM6Avmv5FG3" />
        <meta-data
            android:name="com.baidu.speech.SECRET_KEY"
            android:value="e4DxN5gewACp162txczyVRuJs4UGBhdb" />

        <!-- 启动页 -->
        <activity
            android:name=".ui.splash.SplashActivity"
            android:exported="true"
            android:screenOrientation="landscape"
            android:theme="@style/FullScreenTheme"
            android:hardwareAccelerated="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 登录页 -->
        <activity
            android:name=".ui.login.LoginActivity"
            android:exported="false"
            android:screenOrientation="landscape"
            android:theme="@style/FullScreenTheme"
            android:hardwareAccelerated="true" />

        <!-- 主界面（已重命名，移除了MainActivity2） -->
        <activity
            android:name=".ui.main.MainActivity"
            android:exported="false"
            android:screenOrientation="landscape"
            android:theme="@style/FullScreenTheme"
            android:hardwareAccelerated="true" />

        <!-- 播放服务 -->
        <service
            android:name=".service.UnifiedPlaybackService"
            android:enabled="true"
            android:exported="false" />

        <!-- 播放器页面已迁移到Fragment，不再需要单独的Activity -->
        <!-- PlayerActivity已删除，功能已迁移到PlayerFragment -->

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
    </application>

</manifest>