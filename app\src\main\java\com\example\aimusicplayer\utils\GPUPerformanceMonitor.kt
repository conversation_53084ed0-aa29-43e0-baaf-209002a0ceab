package com.example.aimusicplayer.utils

import android.app.Activity
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.view.ViewTreeObserver
import androidx.annotation.RequiresApi
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicLong

/**
 * GPU性能监控工具
 * 专门用于监控和优化GPU渲染性能，解决OpenGL相关问题
 */
object GPUPerformanceMonitor {
    private const val TAG = "GPUPerformanceMonitor"
    
    // 性能统计
    private val frameCount = AtomicInteger(0)
    private val droppedFrameCount = AtomicInteger(0)
    private val lastFrameTime = AtomicLong(0)
    private val renderingErrors = AtomicInteger(0)
    
    // 监控状态
    private var isMonitoring = false
    private val monitorHandler = Handler(Looper.getMainLooper())
    
    // 性能阈值
    private const val TARGET_FPS = 60
    private const val FRAME_TIME_THRESHOLD = 16.67f // 60fps = 16.67ms per frame
    private const val MAX_DROPPED_FRAMES = 5
    
    /**
     * 开始监控GPU性能
     */
    fun startMonitoring(activity: Activity) {
        if (isMonitoring) {
            Log.d(TAG, "GPU性能监控已在运行")
            return
        }
        
        try {
            isMonitoring = true
            Log.d(TAG, "开始GPU性能监控")
            
            // 重置统计数据
            resetStatistics()
            
            // 监控Activity的渲染性能
            monitorActivityRendering(activity)
            
            // 定期检查性能指标
            startPerformanceCheck()
            
        } catch (e: Exception) {
            Log.e(TAG, "启动GPU性能监控失败", e)
            isMonitoring = false
        }
    }
    
    /**
     * 停止监控
     */
    fun stopMonitoring() {
        if (!isMonitoring) {
            return
        }
        
        isMonitoring = false
        monitorHandler.removeCallbacksAndMessages(null)
        
        // 输出最终统计
        logFinalStatistics()
        
        Log.d(TAG, "GPU性能监控已停止")
    }
    
    /**
     * 监控Activity的渲染性能
     */
    private fun monitorActivityRendering(activity: Activity) {
        try {
            val decorView = activity.window.decorView
            
            // 监控绘制性能
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                decorView.viewTreeObserver.addOnDrawListener {
                    onFrameDrawn()
                }
            }
            
            // 监控布局性能
            decorView.viewTreeObserver.addOnGlobalLayoutListener {
                onLayoutCompleted()
            }
            
            // 检查硬件加速状态
            checkHardwareAcceleration(decorView)
            
        } catch (e: Exception) {
            Log.e(TAG, "监控Activity渲染性能失败", e)
            renderingErrors.incrementAndGet()
        }
    }
    
    /**
     * 帧绘制完成回调
     */
    private fun onFrameDrawn() {
        try {
            val currentTime = System.nanoTime()
            val lastTime = lastFrameTime.get()
            
            if (lastTime > 0) {
                val frameTime = (currentTime - lastTime) / 1_000_000f // 转换为毫秒
                
                if (frameTime > FRAME_TIME_THRESHOLD) {
                    droppedFrameCount.incrementAndGet()
                    Log.v(TAG, "检测到掉帧: ${frameTime}ms")
                }
            }
            
            lastFrameTime.set(currentTime)
            frameCount.incrementAndGet()
            
        } catch (e: Exception) {
            Log.e(TAG, "处理帧绘制回调失败", e)
            renderingErrors.incrementAndGet()
        }
    }
    
    /**
     * 布局完成回调
     */
    private fun onLayoutCompleted() {
        Log.v(TAG, "布局完成")
    }
    
    /**
     * 检查硬件加速状态
     */
    private fun checkHardwareAcceleration(view: View) {
        try {
            val isHardwareAccelerated = view.isHardwareAccelerated
            Log.d(TAG, "硬件加速状态: $isHardwareAccelerated")
            
            if (!isHardwareAccelerated) {
                Log.w(TAG, "警告: 硬件加速未启用，可能影响渲染性能")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "检查硬件加速状态失败", e)
        }
    }
    
    /**
     * 定期检查性能指标
     */
    private fun startPerformanceCheck() {
        val checkRunnable = object : Runnable {
            override fun run() {
                if (isMonitoring) {
                    checkPerformanceMetrics()
                    monitorHandler.postDelayed(this, 5000) // 每5秒检查一次
                }
            }
        }
        
        monitorHandler.postDelayed(checkRunnable, 5000)
    }
    
    /**
     * 检查性能指标
     */
    private fun checkPerformanceMetrics() {
        try {
            val totalFrames = frameCount.get()
            val droppedFrames = droppedFrameCount.get()
            val errors = renderingErrors.get()
            
            if (totalFrames > 0) {
                val dropRate = (droppedFrames.toFloat() / totalFrames) * 100
                
                Log.d(TAG, "性能统计 - 总帧数: $totalFrames, 掉帧: $droppedFrames, 掉帧率: ${String.format("%.2f", dropRate)}%, 错误: $errors")
                
                // 检查是否需要优化
                if (dropRate > 10) {
                    Log.w(TAG, "警告: 掉帧率过高 (${String.format("%.2f", dropRate)}%), 建议优化渲染性能")
                    suggestOptimizations()
                }
                
                if (errors > 5) {
                    Log.w(TAG, "警告: 渲染错误过多 ($errors), 可能存在兼容性问题")
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "检查性能指标失败", e)
        }
    }
    
    /**
     * 建议优化措施
     */
    private fun suggestOptimizations() {
        Log.i(TAG, "性能优化建议:")
        Log.i(TAG, "1. 检查是否启用了硬件加速")
        Log.i(TAG, "2. 减少复杂的自定义绘制操作")
        Log.i(TAG, "3. 优化动画和过渡效果")
        Log.i(TAG, "4. 使用更高效的布局")
        Log.i(TAG, "5. 减少overdraw（过度绘制）")
    }
    
    /**
     * 重置统计数据
     */
    private fun resetStatistics() {
        frameCount.set(0)
        droppedFrameCount.set(0)
        lastFrameTime.set(0)
        renderingErrors.set(0)
    }
    
    /**
     * 输出最终统计
     */
    private fun logFinalStatistics() {
        try {
            val totalFrames = frameCount.get()
            val droppedFrames = droppedFrameCount.get()
            val errors = renderingErrors.get()
            
            Log.i(TAG, "=== GPU性能监控最终报告 ===")
            Log.i(TAG, "总帧数: $totalFrames")
            Log.i(TAG, "掉帧数: $droppedFrames")
            
            if (totalFrames > 0) {
                val dropRate = (droppedFrames.toFloat() / totalFrames) * 100
                Log.i(TAG, "掉帧率: ${String.format("%.2f", dropRate)}%")
                
                val avgFps = if (totalFrames > 0) {
                    val totalTime = System.nanoTime() - lastFrameTime.get()
                    (totalFrames * 1_000_000_000f / totalTime)
                } else 0f
                
                Log.i(TAG, "平均FPS: ${String.format("%.2f", avgFps)}")
            }
            
            Log.i(TAG, "渲染错误: $errors")
            Log.i(TAG, "=== 报告结束 ===")
            
        } catch (e: Exception) {
            Log.e(TAG, "输出最终统计失败", e)
        }
    }
    
    /**
     * 获取当前性能状态
     */
    fun getCurrentPerformanceStatus(): PerformanceStatus {
        val totalFrames = frameCount.get()
        val droppedFrames = droppedFrameCount.get()
        val errors = renderingErrors.get()
        
        val dropRate = if (totalFrames > 0) {
            (droppedFrames.toFloat() / totalFrames) * 100
        } else 0f
        
        return PerformanceStatus(
            totalFrames = totalFrames,
            droppedFrames = droppedFrames,
            dropRate = dropRate,
            renderingErrors = errors,
            isHealthy = dropRate < 5 && errors < 3
        )
    }
    
    /**
     * 性能状态数据类
     */
    data class PerformanceStatus(
        val totalFrames: Int,
        val droppedFrames: Int,
        val dropRate: Float,
        val renderingErrors: Int,
        val isHealthy: Boolean
    )
}
