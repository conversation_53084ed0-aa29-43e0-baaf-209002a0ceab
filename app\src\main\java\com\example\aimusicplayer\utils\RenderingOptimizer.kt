package com.example.aimusicplayer.utils

import android.app.Activity
import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import androidx.annotation.RequiresApi

/**
 * 渲染优化工具类
 * 专门用于解决OpenGL渲染问题和提高UI性能
 * 特别针对"Unable to match the desired swap behavior"错误
 */
object RenderingOptimizer {
    private const val TAG = "RenderingOptimizer"

    /**
     * 优化Activity的渲染设置
     * 解决OpenGL渲染问题
     */
    fun optimizeActivityRendering(activity: Activity) {
        try {
            val window = activity.window

            // 1. 设置硬件加速
            enableHardwareAcceleration(window)

            // 2. 优化窗口格式
            optimizeWindowFormat(window)

            // 3. 设置渲染模式
            optimizeRenderingMode(window)

            // 4. 优化布局参数
            optimizeLayoutParams(window)

            Log.d(TAG, "Activity渲染优化完成: ${activity.javaClass.simpleName}")
        } catch (e: Exception) {
            Log.e(TAG, "Activity渲染优化失败", e)
        }
    }

    /**
     * 启用硬件加速
     */
    private fun enableHardwareAcceleration(window: Window) {
        try {
            // 启用硬件加速
            window.setFlags(
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
            )
            Log.d(TAG, "硬件加速已启用")
        } catch (e: Exception) {
            Log.e(TAG, "启用硬件加速失败", e)
        }
    }

    /**
     * 优化窗口格式
     */
    private fun optimizeWindowFormat(window: Window) {
        try {
            // 设置像素格式为RGBA_8888，提供最佳的颜色质量
            window.setFormat(PixelFormat.RGBA_8888)

            // 如果支持，使用TRANSLUCENT格式以获得更好的混合效果
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                window.statusBarColor = android.graphics.Color.TRANSPARENT
                window.navigationBarColor = android.graphics.Color.TRANSPARENT
            }

            Log.d(TAG, "窗口格式优化完成")
        } catch (e: Exception) {
            Log.e(TAG, "窗口格式优化失败", e)
        }
    }

    /**
     * 优化渲染模式
     */
    private fun optimizeRenderingMode(window: Window) {
        try {
            val decorView = window.decorView

            // 设置系统UI可见性，启用沉浸式模式
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                decorView.systemUiVisibility = (
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                )
            }

            Log.d(TAG, "渲染模式优化完成")
        } catch (e: Exception) {
            Log.e(TAG, "渲染模式优化失败", e)
        }
    }

    /**
     * 优化布局参数
     */
    private fun optimizeLayoutParams(window: Window) {
        try {
            val layoutParams = window.attributes

            // 设置亮度为自动
            layoutParams.screenBrightness = WindowManager.LayoutParams.BRIGHTNESS_OVERRIDE_NONE

            // 优化窗口标志
            layoutParams.flags = layoutParams.flags or
                WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON or
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED

            window.attributes = layoutParams

            Log.d(TAG, "布局参数优化完成")
        } catch (e: Exception) {
            Log.e(TAG, "布局参数优化失败", e)
        }
    }

    /**
     * 优化视图的硬件加速
     */
    fun optimizeViewHardwareAcceleration(view: View) {
        try {
            when {
                // 对于动画视图，启用硬件加速
                isAnimationView(view) -> {
                    view.setLayerType(View.LAYER_TYPE_HARDWARE, null)
                    Log.d(TAG, "动画视图硬件加速已启用: ${view.javaClass.simpleName}")
                }

                // 对于复杂绘制视图，可能需要软件渲染
                isComplexDrawingView(view) -> {
                    view.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
                    Log.d(TAG, "复杂绘制视图软件渲染已启用: ${view.javaClass.simpleName}")
                }

                // 其他视图使用默认设置
                else -> {
                    view.setLayerType(View.LAYER_TYPE_NONE, null)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "视图硬件加速优化失败", e)
        }
    }

    /**
     * 判断是否为动画视图
     */
    private fun isAnimationView(view: View): Boolean {
        return view.javaClass.simpleName.contains("Album") ||
               view.javaClass.simpleName.contains("Cover") ||
               view.javaClass.simpleName.contains("Rotation")
    }

    /**
     * 判断是否为复杂绘制视图
     */
    private fun isComplexDrawingView(view: View): Boolean {
        return view.javaClass.simpleName.contains("Lyric") ||
               view.javaClass.simpleName.contains("Spectrum") ||
               view.javaClass.simpleName.contains("Visualizer")
    }

    /**
     * 优化ViewGroup的渲染性能
     */
    fun optimizeViewGroupRendering(viewGroup: ViewGroup) {
        try {
            // 禁用子视图的绘制缓存（已过时的功能，使用@Suppress抑制警告）
            @Suppress("DEPRECATION")
            viewGroup.isChildrenDrawingCacheEnabled = false

            // 启用绘制顺序优化（已过时的功能，使用@Suppress抑制警告）
            @Suppress("DEPRECATION")
            viewGroup.isChildrenDrawnWithCacheEnabled = false

            // 设置动画缓存（已过时的功能，使用@Suppress抑制警告）
            @Suppress("DEPRECATION")
            viewGroup.isAnimationCacheEnabled = false

            // 递归优化子视图
            for (i in 0 until viewGroup.childCount) {
                val child = viewGroup.getChildAt(i)
                optimizeViewHardwareAcceleration(child)

                if (child is ViewGroup) {
                    optimizeViewGroupRendering(child)
                }
            }

            Log.d(TAG, "ViewGroup渲染优化完成: ${viewGroup.javaClass.simpleName}")
        } catch (e: Exception) {
            Log.e(TAG, "ViewGroup渲染优化失败", e)
        }
    }

    /**
     * 检查设备的硬件加速支持情况
     */
    fun checkHardwareAccelerationSupport(context: Context): Boolean {
        return try {
            val activity = context as? Activity
            val isHardwareAccelerated = activity?.window?.decorView?.isHardwareAccelerated ?: false

            Log.d(TAG, "硬件加速支持状态: $isHardwareAccelerated")
            isHardwareAccelerated
        } catch (e: Exception) {
            Log.e(TAG, "检查硬件加速支持失败", e)
            false
        }
    }

    /**
     * 监控渲染性能
     */
    @RequiresApi(Build.VERSION_CODES.JELLY_BEAN)
    fun monitorRenderingPerformance(view: View) {
        try {
            view.viewTreeObserver.addOnDrawListener {
                // 监控绘制性能
                Log.v(TAG, "视图绘制: ${view.javaClass.simpleName}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "渲染性能监控设置失败", e)
        }
    }
}
