package com.example.aimusicplayer.ui.login

import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.util.Log
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.Button
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import com.example.aimusicplayer.MusicApplication
import com.example.aimusicplayer.R
import com.example.aimusicplayer.databinding.ActivityLoginBinding
import com.example.aimusicplayer.ui.main.MainActivity
import com.example.aimusicplayer.utils.ImageUtils
import com.example.aimusicplayer.utils.PerformanceUtils
import com.example.aimusicplayer.utils.QrCodeProcessor
import com.example.aimusicplayer.utils.RenderingOptimizer
import com.example.aimusicplayer.viewmodel.LoginViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * 登录页面Activity
 * 提供扫码登录、手机号登录和游客登录功能
 * 使用MVVM架构和Hilt依赖注入
 */
@AndroidEntryPoint
class LoginActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "LoginActivity"
    }

    // ViewBinding
    private lateinit var binding: ActivityLoginBinding

    // QR登录相关
    private var qrDialog: AlertDialog? = null
    private lateinit var qrImageView: ImageView
    private lateinit var qrLoadingProgressBar: ProgressBar
    private lateinit var qrStatusTextView: TextView
    private var checkQrStatus: Runnable? = null

    // 手机登录相关
    private var phoneDialog: AlertDialog? = null

    // 应用实例
    private lateinit var musicApplication: MusicApplication

    // ViewModel
    private lateinit var viewModel: LoginViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 优化渲染性能，解决OpenGL问题
        RenderingOptimizer.optimizeActivityRendering(this)

        // 使用现代API设置全屏模式
        PerformanceUtils.setFullscreen(this, true)

        // 初始化ViewBinding
        binding = ActivityLoginBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 优化根视图的渲染性能
        RenderingOptimizer.optimizeViewGroupRendering(binding.root as ViewGroup)

        // 初始化ViewModel
        viewModel = ViewModelProvider(this)[LoginViewModel::class.java]

        // 获取应用实例
        musicApplication = application as MusicApplication

        // 初始化UI组件
        initializeUI()

        // 设置观察者
        setupObservers()
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus) {
            // 当窗口获得焦点时，再次设置全屏模式
            PerformanceUtils.setFullscreen(this, true)
        }
    }

    /**
     * 初始化UI组件
     */
    private fun initializeUI() {
        // 使用ViewBinding获取UI组件
        val btnQrcodeLogin = binding.btnQrcodeLogin
        val btnPhoneLogin = binding.btnPhoneLogin
        val btnGuestLogin = binding.btnGuestLogin
        @Suppress("UNUSED_VARIABLE")
        val progressBar = binding.progressBar

        // 添加按钮动画效果
        val buttons = arrayOf(btnQrcodeLogin, btnPhoneLogin, btnGuestLogin)
        for (button in buttons) {
            button.alpha = 0f
            button.animate()
                .alpha(1f)
                .setDuration(400)
                .setStartDelay(300)
                .start()

            // 添加按压效果增强
            button.setOnTouchListener { v, event ->
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        v.animate().scaleX(0.96f).scaleY(0.96f).setDuration(100).start()
                    }
                    MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                        v.animate().scaleX(1f).scaleY(1f).setDuration(100).start()
                    }
                }
                false
            }
        }

        // 设置点击事件监听器
        setButtonClickListeners()
    }

    /**
     * 设置观察者
     */
    private fun setupObservers() {
        // 观察登录状态
        viewModel.loginState.observe(this) { loginState ->
            Log.d(TAG, "登录状态变更: $loginState")
            when (loginState) {
                LoginViewModel.LoginState.SUCCESS -> {
                    // 登录成功，导航到主界面
                    Log.d(TAG, "登录成功，准备导航到主界面")
                    // 使用Handler确保在UI线程上执行
                    Handler(Looper.getMainLooper()).post {
                        navigateToMainActivity()
                    }
                }
                LoginViewModel.LoginState.FAILED -> {
                    // 登录失败，显示错误信息
                    val errorMsg = viewModel.errorMessage.value
                    if (!errorMsg.isNullOrEmpty()) {
                        showErrorDialog("登录失败", errorMsg)
                    }
                }
                LoginViewModel.LoginState.IDLE -> {
                    // 初始状态，不做处理
                }
                null -> {
                    // 处理null值情况
                    Log.w(TAG, "收到null登录状态")
                }
            }
        }

        // 观察加载状态
        viewModel.loading.observe(this) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        }

        // 观察错误信息
        viewModel.errorMessage.observe(this) { errorMsg ->
            if (!errorMsg.isNullOrEmpty()) {
                Log.e(TAG, "错误信息: $errorMsg")
                // 只有在登录失败状态下才显示错误对话框，避免重复显示
                if (viewModel.loginState.value == LoginViewModel.LoginState.FAILED) {
                    showErrorDialog("登录失败", errorMsg)
                }
            }
        }
    }

    /**
     * 设置按钮点击事件监听器
     */
    private fun setButtonClickListeners() {
        // 扫码登录按钮
        binding.btnQrcodeLogin.setOnClickListener { showQrLoginDialog() }

        // 手机号登录按钮
        binding.btnPhoneLogin.setOnClickListener { showPhoneLoginDialog() }

        // 游客登录按钮
        binding.btnGuestLogin.setOnClickListener {
            // 禁用登录按钮防止重复点击
            binding.btnGuestLogin.isEnabled = false
            binding.btnQrcodeLogin.isEnabled = false
            binding.btnPhoneLogin.isEnabled = false

            // 显示加载动画
            binding.progressBar.visibility = View.VISIBLE

            // 执行游客登录
            viewModel.loginAsGuest()

            // 观察登录状态
            viewModel.loginState.observe(this) { loginState ->
                if (loginState == LoginViewModel.LoginState.FAILED) {
                    // 登录失败，恢复按钮状态
                    binding.btnGuestLogin.isEnabled = true
                    binding.btnQrcodeLogin.isEnabled = true
                    binding.btnPhoneLogin.isEnabled = true

                    // 隐藏加载动画
                    binding.progressBar.visibility = View.GONE

                    // 显示错误信息
                    val errorMsg = viewModel.errorMessage.value
                    if (!errorMsg.isNullOrEmpty()) {
                        Toast.makeText(this, errorMsg, Toast.LENGTH_SHORT).show()
                    }
                }
            }
        }
    }

    /**
     * 显示二维码登录对话框
     */
    private fun showQrLoginDialog() {
        // 创建自定义对话框视图
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_qr_login, null)
        qrImageView = dialogView.findViewById(R.id.qr_image)
        qrLoadingProgressBar = dialogView.findViewById(R.id.qr_loading)
        qrStatusTextView = dialogView.findViewById(R.id.qr_status)
        val btnCancel = dialogView.findViewById<Button>(R.id.btn_cancel)

        // 获取二维码错误相关视图
        val qrErrorContainer = dialogView.findViewById<LinearLayout>(R.id.qr_error_container)
        val btnReloadQr = dialogView.findViewById<Button>(R.id.btn_reload_qr)
        val qrErrorText = dialogView.findViewById<TextView>(R.id.qr_error_text)
        val tvTip = dialogView.findViewById<TextView>(R.id.tv_tip)

        // 构建对话框
        val builder = AlertDialog.Builder(this)
        builder.setView(dialogView)
        qrDialog = builder.create()

        // 设置取消按钮事件
        btnCancel.setOnClickListener { qrDialog?.dismiss() }

        // 设置重新加载按钮事件
        btnReloadQr.setOnClickListener {
            // 隐藏错误视图
            qrErrorContainer.visibility = View.GONE
            // 显示加载进度条
            qrLoadingProgressBar.visibility = View.VISIBLE
            // 重新获取二维码
            viewModel.getQrKey()
        }

        // 观察二维码图片URL
        viewModel.qrCodeProcessor.qrImageUrl.observe(this) { qrImgUrl ->
            if (!qrImgUrl.isNullOrEmpty()) {
                Log.d(TAG, "收到二维码图片URL: ${if (qrImgUrl.length > 100) qrImgUrl.substring(0, 100) + "..." else qrImgUrl}")

                // 使用QrCodeProcessor加载二维码图片
                viewModel.qrCodeProcessor.loadQrImage(qrImgUrl, qrImageView)
                qrLoadingProgressBar.visibility = View.GONE
                qrImageView.visibility = View.VISIBLE
            } else {
                Log.e(TAG, "收到空的二维码图片URL")
                qrLoadingProgressBar.visibility = View.GONE
                qrErrorContainer.visibility = View.VISIBLE
                qrErrorText.text = "获取二维码失败，请重试"
            }
        }

        // 观察二维码状态
        viewModel.qrCodeProcessor.qrStatus.observe(this) { status ->
            status?.let {
                when (status) {
                    QrCodeProcessor.QrStatus.LOADING -> {
                        qrLoadingProgressBar.visibility = View.VISIBLE
                        qrImageView.visibility = View.GONE
                        qrErrorContainer.visibility = View.GONE
                        qrStatusTextView.visibility = View.GONE
                        tvTip.text = "正在加载二维码..."
                    }
                    QrCodeProcessor.QrStatus.WAITING -> {
                        qrLoadingProgressBar.visibility = View.GONE
                        qrImageView.visibility = View.VISIBLE
                        qrErrorContainer.visibility = View.GONE
                        qrStatusTextView.visibility = View.GONE
                        tvTip.text = "打开网易云音乐APP，点击右上角+，选择扫一扫扫描上方二维码"
                    }
                    QrCodeProcessor.QrStatus.SCANNED -> {
                        qrLoadingProgressBar.visibility = View.GONE
                        qrImageView.visibility = View.VISIBLE
                        qrErrorContainer.visibility = View.GONE
                        qrStatusTextView.visibility = View.VISIBLE
                        qrStatusTextView.text = "请在手机上确认登录"
                        tvTip.text = "已扫描二维码，请在手机上确认登录"
                    }
                    QrCodeProcessor.QrStatus.CONFIRMED -> {
                        // 二维码验证成功，显示正在获取用户信息的提示
                        qrLoadingProgressBar.visibility = View.VISIBLE
                        qrImageView.visibility = View.GONE
                        qrErrorContainer.visibility = View.GONE
                        qrStatusTextView.visibility = View.VISIBLE
                        qrStatusTextView.text = "登录成功，正在获取用户信息..."
                        tvTip.text = "请稍候，正在连接服务器获取账号信息..."

                        // 禁用取消按钮，防止用户在获取用户信息过程中关闭对话框
                        btnCancel.isEnabled = false

                        // 观察登录状态，当获取到用户信息后关闭对话框
                        viewModel.loginState.observe(this) { loginState ->
                            if (loginState == LoginViewModel.LoginState.SUCCESS) {
                                // 登录完全成功，关闭对话框
                                qrDialog?.dismiss()
                            } else if (loginState == LoginViewModel.LoginState.FAILED) {
                                // 获取用户信息失败，显示错误
                                qrLoadingProgressBar.visibility = View.GONE
                                qrErrorContainer.visibility = View.VISIBLE
                                val errorMsg = viewModel.errorMessage.value
                                // 简化错误消息，避免过长
                                val displayError = when {
                                    errorMsg.isNullOrEmpty() -> "获取用户信息失败，请重试"
                                    errorMsg.contains("未能获取登录凭证") -> "登录失败：未能获取登录凭证"
                                    errorMsg.length > 30 -> "${errorMsg.substring(0, 30)}..."
                                    else -> errorMsg
                                }
                                qrErrorText.text = displayError

                                // 重新启用取消按钮
                                btnCancel.isEnabled = true
                            }
                        }
                    }
                    QrCodeProcessor.QrStatus.EXPIRED -> {
                        qrLoadingProgressBar.visibility = View.GONE
                        qrImageView.visibility = View.GONE
                        qrErrorContainer.visibility = View.VISIBLE
                        qrErrorText.text = "二维码已过期，请重新获取"
                    }
                    QrCodeProcessor.QrStatus.ERROR -> {
                        qrLoadingProgressBar.visibility = View.GONE
                        qrImageView.visibility = View.GONE
                        qrErrorContainer.visibility = View.VISIBLE
                        val errorMsg = viewModel.errorMessage.value
                        qrErrorText.text = errorMsg ?: "二维码加载失败，请重试"
                    }
                }
            }
        }

        // 显示对话框
        qrDialog?.show()

        // 获取二维码登录Key
        viewModel.getQrKey()
    }

    /**
     * 执行验证码登录
     */
    private fun performCaptchaLogin(phone: String, captcha: String) {
        // 手机号格式验证
        if (!isValidPhoneNumber(phone)) {
            showErrorDialog("手机号格式错误", "请输入正确的手机号码")
            return
        }

        // 验证码格式验证
        if (captcha.length != 4 && captcha.length != 6) {
            showErrorDialog("验证码错误", "验证码应为4位或6位数字")
            return
        }

        // 禁用对话框按钮，防止重复点击
        val btnLogin = phoneDialog?.findViewById<Button>(R.id.btn_login)
        val btnCancel = phoneDialog?.findViewById<Button>(R.id.btn_cancel)
        btnLogin?.isEnabled = false
        btnCancel?.isEnabled = false

        // 显示加载动画
        val progressBar = phoneDialog?.findViewById<ProgressBar>(R.id.progress_bar)
        progressBar?.visibility = View.VISIBLE

        // 不再显示文字提示，只使用动画
        val statusTextView = phoneDialog?.findViewById<TextView>(R.id.tv_status)
        statusTextView?.visibility = View.GONE

        // 创建验证码状态LiveData
        val captchaState = MutableLiveData<LoginViewModel.CaptchaState>()

        // 观察验证码状态
        captchaState.observe(this) { state ->
            when (state) {
                LoginViewModel.CaptchaState.VERIFIED -> {
                    // 验证码验证成功，继续登录
                    Log.d(TAG, "验证码验证成功，继续登录")
                    Toast.makeText(this, "验证码正确，正在登录...", Toast.LENGTH_SHORT).show()
                    // 使用ViewModel执行验证码登录
                    viewModel.loginWithCaptcha(phone, captcha)
                }
                LoginViewModel.CaptchaState.INVALID -> {
                    // 验证码验证失败
                    Log.d(TAG, "验证码验证失败")
                    // 恢复按钮状态
                    btnLogin?.isEnabled = true
                    btnCancel?.isEnabled = true
                    // 显示错误信息
                    val errorMsg = viewModel.errorMessage.value
                    if (!errorMsg.isNullOrEmpty()) {
                        Toast.makeText(this, errorMsg, Toast.LENGTH_SHORT).show()
                    } else {
                        Toast.makeText(this, "验证码错误，请重新输入", Toast.LENGTH_SHORT).show()
                    }
                }
                LoginViewModel.CaptchaState.ERROR -> {
                    // 验证码验证出错
                    Log.d(TAG, "验证码验证出错")
                    // 恢复按钮状态
                    btnLogin?.isEnabled = true
                    btnCancel?.isEnabled = true
                    // 显示错误信息
                    val errMsg = viewModel.errorMessage.value
                    if (!errMsg.isNullOrEmpty()) {
                        Toast.makeText(this, errMsg, Toast.LENGTH_SHORT).show()
                    } else {
                        Toast.makeText(this, "验证验证码失败，请重试", Toast.LENGTH_SHORT).show()
                    }
                }
                else -> {
                    // 其他状态
                }
            }
        }

        // 先验证验证码是否正确
        viewModel.verifyCaptcha(phone, captcha, captchaState)

        // 观察登录状态，在ViewModel中已经设置了相应的状态
        // 当登录成功时，会自动关闭对话框并跳转到主界面
        // 当登录失败时，会显示错误信息
        viewModel.loginState.observe(this) { loginState ->
            // 恢复按钮状态
            btnLogin?.isEnabled = true
            btnCancel?.isEnabled = true

            if (loginState == LoginViewModel.LoginState.SUCCESS) {
                // 登录成功，关闭对话框
                phoneDialog?.dismiss()
                // 导航到主界面在ViewModel的观察者中处理
            }
        }
    }

    /**
     * 执行手机号登录
     */
    private fun performPhoneLogin(phone: String, password: String) {
        // 输入验证
        if (!isValidPhoneNumber(phone)) {
            showErrorDialog("输入错误", "请输入有效的手机号码")
            return
        }

        if (!isValidPassword(password)) {
            showErrorDialog("输入错误", "密码至少需要6个字符且不含空格")
            return
        }

        // 禁用对话框按钮，防止重复点击
        val btnLogin = phoneDialog?.findViewById<Button>(R.id.btn_login)
        val btnCancel = phoneDialog?.findViewById<Button>(R.id.btn_cancel)
        btnLogin?.isEnabled = false
        btnCancel?.isEnabled = false

        // 显示加载动画
        val progressBar = phoneDialog?.findViewById<ProgressBar>(R.id.progress_bar)
        progressBar?.visibility = View.VISIBLE

        // 不再显示文字提示，只使用动画
        val statusTextView = phoneDialog?.findViewById<TextView>(R.id.tv_status)
        statusTextView?.visibility = View.GONE

        // 使用ViewModel执行手机号密码登录
        viewModel.loginWithPhone(phone, password)

        // 观察登录状态，在ViewModel中已经设置了相应的状态
        viewModel.loginState.observe(this) { loginState ->
            // 恢复按钮状态
            btnLogin?.isEnabled = true
            btnCancel?.isEnabled = true

            if (loginState == LoginViewModel.LoginState.SUCCESS) {
                // 登录成功，关闭对话框
                phoneDialog?.dismiss()
                // 导航到主界面在ViewModel的观察者中处理
            } else if (loginState == LoginViewModel.LoginState.FAILED) {
                // 隐藏加载动画
                progressBar?.visibility = View.GONE

                // 显示错误信息
                val errorMsg = viewModel.errorMessage.value
                if (!errorMsg.isNullOrEmpty()) {
                    Toast.makeText(this, errorMsg, Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    /**
     * 导航到主活动
     */
    private fun navigateToMainActivity() {
        try {
            Log.d(TAG, "开始导航到主界面")

            // 确保在主线程上执行
            if (Looper.myLooper() != Looper.getMainLooper()) {
                Log.d(TAG, "当前不在主线程，切换到主线程执行")
                Handler(Looper.getMainLooper()).post { navigateToMainActivity() }
                return
            }

            // 创建Intent并设置标志
            val intent = Intent(this, MainActivity::class.java)
            // 清除任务栈中的其他Activity，确保MainActivity成为新任务的根Activity
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK

            Log.d(TAG, "准备启动MainActivity")
            startActivity(intent)
            Log.d(TAG, "已启动MainActivity")

            // 添加过渡动画
            @Suppress("DEPRECATION")
            overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out)

            // 立即结束当前Activity
            Log.d(TAG, "立即关闭LoginActivity")
            finish()
        } catch (e: Exception) {
            Log.e(TAG, "导航到主界面时出错", e)
            // 显示错误提示
            Toast.makeText(this, "启动主界面失败: ${e.message}", Toast.LENGTH_LONG).show()

            // 尝试使用备用方法启动
            try {
                Log.d(TAG, "尝试使用备用方法启动MainActivity")
                val intent = Intent()
                intent.setClassName(packageName, "com.example.aimusicplayer.ui.main.MainActivity")
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                startActivity(intent)
                finish()
            } catch (ex: Exception) {
                Log.e(TAG, "备用启动方法也失败", ex)
            }
        }
    }

    /**
     * 显示手机号登录对话框
     */
    private fun showPhoneLoginDialog() {
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_phone_login, null)
        val etPhone = dialogView.findViewById<EditText>(R.id.et_phone)
        val etPassword = dialogView.findViewById<EditText>(R.id.et_password)
        @Suppress("UNUSED_VARIABLE")
        val etCaptcha = dialogView.findViewById<EditText>(R.id.et_captcha)
        // val btnSendCaptcha = dialogView.findViewById<Button>(R.id.btn_send_captcha) // TODO: Uncomment if R.id.btn_send_captcha exists
        val btnLogin = dialogView.findViewById<Button>(R.id.btn_login)
        val btnCancel = dialogView.findViewById<Button>(R.id.btn_cancel)
        // val tvSwitchToPassword = dialogView.findViewById<TextView>(R.id.tv_switch_to_password) // TODO: Uncomment if R.id.tv_switch_to_password exists
        // val tvSwitchToCaptcha = dialogView.findViewById<TextView>(R.id.tv_switch_to_captcha) // TODO: Uncomment if R.id.tv_switch_to_captcha exists
        // val layoutPassword = dialogView.findViewById<LinearLayout>(R.id.layout_password) // TODO: Uncomment if R.id.layout_password exists
        // val layoutCaptcha = dialogView.findViewById<LinearLayout>(R.id.layout_captcha) // TODO: Uncomment if R.id.layout_captcha exists
        val progressBar = dialogView.findViewById<ProgressBar>(R.id.progress_bar)
        val tvStatus = dialogView.findViewById<TextView>(R.id.tv_status)

        val builder = AlertDialog.Builder(this)
        builder.setView(dialogView)
        phoneDialog = builder.create()

        // 默认显示验证码登录
        // TODO: Re-enable these lines once the view IDs are confirmed
        // layoutPassword.visibility = View.GONE
        // layoutCaptcha.visibility = View.VISIBLE
        // tvSwitchToPassword.visibility = View.VISIBLE
        // tvSwitchToCaptcha.visibility = View.GONE

        // 切换到密码登录
        // tvSwitchToPassword.setOnClickListener {
        //     layoutPassword.visibility = View.VISIBLE
        //     layoutCaptcha.visibility = View.GONE
        //     tvSwitchToPassword.visibility = View.GONE
        //     tvSwitchToCaptcha.visibility = View.VISIBLE
        //     etCaptcha.setText("") // 清空验证码
        // }

        // 切换到验证码登录
        // tvSwitchToCaptcha.setOnClickListener {
        //     layoutPassword.visibility = View.GONE
        //     layoutCaptcha.visibility = View.VISIBLE
        //     tvSwitchToPassword.visibility = View.VISIBLE
        //     tvSwitchToCaptcha.visibility = View.GONE
        //     etPassword.setText("") // 清空密码
        // }

        // 发送验证码按钮
        // btnSendCaptcha.setOnClickListener { // TODO: Uncomment if R.id.btn_send_captcha exists
        //     val phone = etPhone.text.toString()
        //     if (isValidPhoneNumber(phone)) {
        //         btnSendCaptcha.isEnabled = false
        //         viewModel.sendCaptcha(phone)
        //         // 启动倒计时
        //         var countdown = 60
        //         val handler = Handler(Looper.getMainLooper())
        //         val runnable = object : Runnable {
        //             override fun run() {
        //                 if (countdown > 0) {
        //                     btnSendCaptcha.text = "重新发送(${countdown}s)"
        //                     countdown--
        //                     handler.postDelayed(this, 1000)
        //                 } else {
        //                     btnSendCaptcha.text = "发送验证码"
        //                     btnSendCaptcha.isEnabled = true
        //                 }
        //             }
        //         }
        //         handler.post(runnable)
        //     } else {
        //         Toast.makeText(this, "请输入正确的手机号码", Toast.LENGTH_SHORT).show()
        //     }
        // }

        // 登录按钮
        btnLogin.setOnClickListener {
            val phone = etPhone.text.toString()
            // TODO: Re-enable this check once layoutCaptcha is confirmed
            // if (layoutCaptcha.visibility == View.VISIBLE) {
            //     // 验证码登录
            //     val captcha = etCaptcha.text.toString()
            //     performCaptchaLogin(phone, captcha)
            // } else {
            //     // 密码登录
            //     val password = etPassword.text.toString()
            //     performPhoneLogin(phone, password)
            // }
            // For now, assume password login
            val password = etPassword.text.toString()
            performPhoneLogin(phone, password)
        }

        // 取消按钮
        btnCancel.setOnClickListener {
            phoneDialog?.dismiss()
        }

        // 观察ViewModel的加载状态
        viewModel.loading.observe(this) { isLoading ->
            progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            tvStatus.visibility = if (isLoading) View.VISIBLE else View.GONE
            tvStatus.text = if (isLoading) "正在登录..." else ""
        }

        // 观察ViewModel的错误信息
        viewModel.errorMessage.observe(this) { errorMessage ->
            if (!errorMessage.isNullOrEmpty()) {
                Toast.makeText(this, errorMessage, Toast.LENGTH_SHORT).show()
            }
        }

        phoneDialog?.show()
    }

    /**
     * 显示错误对话框
     */
    private fun showErrorDialog(title: String, message: String) {
        if (isFinishing || isDestroyed) {
            return  // 防止在Activity销毁后显示对话框
        }

        AlertDialog.Builder(this)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton("确定", null)
            .setCancelable(true)
            .show()
    }

    /**
     * 验证手机号格式
     */
    private fun isValidPhoneNumber(phone: String): Boolean {
        // 中国大陆手机号格式：1开头的11位数字
        return phone.matches(Regex("^1[3-9]\\d{9}$"))
    }

    /**
     * 验证密码强度
     */
    private fun isValidPassword(password: String): Boolean {
        // 至少6位且不包含空格
        return password.length >= 6 && !password.contains(" ")
    }
}